import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Trash2, Settings, X, Copy } from "lucide-react";
import { useProjectDetail } from "@/contexts/project-detail-context";

export function MultiGroupConfigDialog({
  open,
  onOpenChange,
  inputName = "",
  functionName = "",
  initialGroups = [],
  onSave = () => {},
}) {
  const { projectItems } = useProjectDetail();
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [usePercentage, setUsePercentage] = useState(false);

  // Load lighting items from projectItems
  const lightingItems = useMemo(() => {
    return projectItems?.lighting || [];
  }, [projectItems]);

  // Prepare combobox options
  const lightingOptions = useMemo(() => {
    return lightingItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [lightingItems]);

  // Initialize selected groups
  useEffect(() => {
    if (open) {
      // Initialize with enhanced data structure including preset values
      const enhancedGroups = (initialGroups || []).map((group) => ({
        lightingId: group.lightingId,
        value: group.value || "100",
        preset: group.preset || 255, // Raw value (0-255)
        presetPercent: group.presetPercent || 100, // Percentage value (0-100%)
      }));
      setSelectedGroups(enhancedGroups);
      setUsePercentage(false); // Default to raw values
    }
  }, [open, initialGroups]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    onSave(selectedGroups);
    handleClose();
  };

  const handleRemoveGroup = (index) => {
    setSelectedGroups((prev) => prev.filter((_, i) => i !== index));
  };

  const handleGroupChange = (index, lightingId) => {
    setSelectedGroups((prev) =>
      prev.map((group, i) => (i === index ? { ...group, lightingId } : group))
    );
  };

  const handleValueChange = (index, value) => {
    setSelectedGroups((prev) =>
      prev.map((group, i) => {
        if (i === index) {
          const numValue = parseInt(value) || 0;
          if (usePercentage) {
            // Update percentage and calculate raw value
            const clampedPercent = Math.max(0, Math.min(100, numValue));
            const rawValue = Math.round((clampedPercent * 255) / 100);
            return {
              ...group,
              value: value,
              presetPercent: clampedPercent,
              preset: rawValue,
            };
          } else {
            // Update raw value and calculate percentage
            const clampedRaw = Math.max(0, Math.min(255, numValue));
            const percentValue = Math.round((clampedRaw * 100) / 255);
            return {
              ...group,
              value: value,
              preset: clampedRaw,
              presetPercent: percentValue,
            };
          }
        }
        return group;
      })
    );
  };

  // Get available lighting groups (not yet selected)
  const availableLightingGroups = useMemo(() => {
    const selectedIds = new Set(
      selectedGroups.map((group) => group.lightingId).filter(Boolean)
    );
    return lightingItems.filter((item) => !selectedIds.has(item.id));
  }, [lightingItems, selectedGroups]);

  // Add group from available list
  const handleAddFromAvailable = (lightingItem) => {
    const newGroup = {
      lightingId: lightingItem.id,
      value: usePercentage ? "100%" : "255",
      preset: 255, // Default to max brightness
      presetPercent: 100,
    };
    setSelectedGroups((prev) => [...prev, newGroup]);
  };

  // Add all available groups
  const handleAddAllGroups = () => {
    const newGroups = availableLightingGroups.map((item) => ({
      lightingId: item.id,
      value: usePercentage ? "100%" : "255",
      preset: 255,
      presetPercent: 100,
    }));
    setSelectedGroups((prev) => [...prev, ...newGroups]);
  };

  // Clear all groups
  const handleClearAllGroups = () => {
    setSelectedGroups([]);
  };

  // Toggle percentage display
  const handleTogglePercentage = (checked) => {
    setUsePercentage(checked);
    // Update display values for all groups
    setSelectedGroups((prev) =>
      prev.map((group) => ({
        ...group,
        value: checked ? `${group.presetPercent}%` : group.preset.toString(),
      }))
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent
        className="sm:max-w-5xl max-h-[90vh] overflow-y-auto"
        aria-describedby="multi-group-description"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Multiple Group Configuration
          </DialogTitle>
          <DialogDescription id="multi-group-description">
            Configure multiple lighting groups for {inputName} - {functionName}
          </DialogDescription>
        </DialogHeader>

        {/* Percentage Toggle */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            id="percentage-toggle"
            checked={usePercentage}
            onCheckedChange={handleTogglePercentage}
          />
          <Label htmlFor="percentage-toggle" className="text-sm font-medium">
            Show values as percentage (0-100%) instead of raw values (0-255)
          </Label>
        </div>

        {/* Two-column layout for Selected Groups and Available Groups */}
        <div className="grid grid-cols-2 gap-4">
          {/* Selected Groups - Left Side */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base">
                Selected Groups
                <Badge variant="secondary" className="ml-2">
                  {selectedGroups.length} Groups
                </Badge>
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddAllGroups}
                  disabled={availableLightingGroups.length === 0}
                  className="flex items-center gap-1"
                >
                  <Copy className="h-3 w-3" />
                  Add All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearAllGroups}
                  disabled={selectedGroups.length === 0}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <X className="h-3 w-3" />
                  Clear All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {selectedGroups.length > 0 ? (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3 pr-4">
                    {selectedGroups.map((group, index) => {
                      const lightingItem = lightingItems.find(
                        (item) => item.id === group.lightingId
                      );
                      return (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 border rounded-lg"
                        >
                          <div className="flex-1 space-y-2">
                            <Label className="text-sm font-medium">
                              {lightingItem
                                ? `${lightingItem.name || "Unnamed"} (${
                                    lightingItem.address
                                  })`
                                : `Group ${index + 1}`}
                            </Label>
                            <Combobox
                              options={lightingOptions}
                              value={group.lightingId?.toString() || ""}
                              onValueChange={(value) =>
                                handleGroupChange(
                                  index,
                                  value ? parseInt(value) : null
                                )
                              }
                              placeholder="Select lighting group..."
                              emptyText="No lighting found"
                            />
                          </div>
                          <div className="w-24 space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              {usePercentage ? "Percent" : "Raw Value"}
                            </Label>
                            <input
                              type="number"
                              min={usePercentage ? 0 : 0}
                              max={usePercentage ? 100 : 255}
                              value={
                                usePercentage
                                  ? group.presetPercent
                                  : group.preset
                              }
                              onChange={(e) =>
                                handleValueChange(index, e.target.value)
                              }
                              className="w-full px-2 py-1 text-sm border rounded"
                              placeholder={usePercentage ? "0-100" : "0-255"}
                            />
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveGroup(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Groups Selected</p>
                  <p className="text-sm mb-4">
                    Select lighting groups from the available list on the right.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Available Groups - Right Side */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">
                Available Groups
                <Badge variant="outline" className="ml-2">
                  {availableLightingGroups.length} Available
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-2 pr-4">
                  {availableLightingGroups.length > 0 ? (
                    availableLightingGroups.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div>
                          <div className="font-medium text-sm">
                            {item.name || `Group ${item.address}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Address: {item.address}
                          </div>
                          {item.description && (
                            <div className="text-xs text-muted-foreground">
                              {item.description}
                            </div>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleAddFromAvailable(item)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <p className="text-sm">No available groups</p>
                      <p className="text-xs">
                        All lighting groups have been selected
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
