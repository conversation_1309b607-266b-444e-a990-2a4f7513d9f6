import React, { useState, useEffect, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Trash2, Settings, X, Copy } from "lucide-react";
import { useProjectDetail } from "@/contexts/project-detail-context";

export function MultiGroupConfigDialog({
  open,
  onOpenChange,
  inputName = "",
  functionName = "",
  initialGroups = [],
  onSave = () => {},
}) {
  const { projectItems } = useProjectDetail();
  const [activeGroups, setActiveGroups] = useState([]);
  const [inactiveGroups, setInactiveGroups] = useState([]);
  const [usePercentage, setUsePercentage] = useState(false);

  // Load lighting items from projectItems
  const lightingItems = useMemo(() => {
    return projectItems?.lighting || [];
  }, [projectItems]);

  // Prepare combobox options
  const lightingOptions = useMemo(() => {
    return lightingItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [lightingItems]);

  // Initialize groups from legacy format or new format
  useEffect(() => {
    if (open) {
      // Handle legacy format (simple array) or new format (object with activeGroups/inactiveGroups)
      if (Array.isArray(initialGroups)) {
        // Legacy format - convert to active groups
        setActiveGroups(
          initialGroups.map((group) => ({
            lightingId: group.lightingId,
            preset: group.value ? parseInt(group.value) : 100,
            presetPercent: group.value
              ? Math.round((parseInt(group.value) * 100) / 255)
              : 100,
          }))
        );
        setInactiveGroups([]);
        setUsePercentage(false);
      } else if (initialGroups && typeof initialGroups === "object") {
        // New format
        setActiveGroups(initialGroups.activeGroups || []);
        setInactiveGroups(initialGroups.inactiveGroups || []);
        setUsePercentage(initialGroups.usePercentage || false);
      } else {
        // Empty initialization
        setActiveGroups([]);
        setInactiveGroups([]);
        setUsePercentage(false);
      }
    }
  }, [open, initialGroups]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    // Save in new format
    const configData = {
      activeGroups,
      inactiveGroups,
      usePercentage,
    };
    onSave(configData);
    handleClose();
  };

  // Get available lighting groups (not in active or inactive groups)
  const availableLightingGroups = useMemo(() => {
    const usedIds = new Set(
      [
        ...activeGroups.map((group) => group.lightingId),
        ...inactiveGroups.map((group) => group.lightingId),
      ].filter(Boolean)
    );
    return lightingItems.filter((item) => !usedIds.has(item.id));
  }, [lightingItems, activeGroups, inactiveGroups]);

  // Helper function to convert between raw (0-255) and percentage (0-100%)
  const rawToPercent = (rawValue) => Math.round((rawValue * 100) / 255);
  const percentToRaw = (percentValue) => Math.round((percentValue * 255) / 100);

  // Add group to active list
  const handleAddToActive = (lightingItem) => {
    const newGroup = {
      lightingId: lightingItem.id,
      preset: 255, // Default to max brightness
      presetPercent: 100,
    };
    setActiveGroups((prev) => [...prev, newGroup]);
  };

  // Add group to inactive list
  const handleAddToInactive = (lightingItem) => {
    const newGroup = {
      lightingId: lightingItem.id,
      preset: 0, // Default to off
      presetPercent: 0,
    };
    setInactiveGroups((prev) => [...prev, newGroup]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent
        className="sm:max-w-5xl max-h-[90vh] overflow-y-auto"
        aria-describedby="multi-group-description"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Multiple Group Configuration
          </DialogTitle>
          <DialogDescription id="multi-group-description">
            Configure multiple lighting groups for {inputName} - {functionName}
          </DialogDescription>
        </DialogHeader>

        {/* Two-column layout for Selected Groups and Available Groups */}
        <div className="grid grid-cols-2 gap-4">
          {/* Selected Groups - Left Side */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base">
                Selected Groups
                <Badge variant="secondary" className="ml-2">
                  {selectedGroups.length} Groups
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedGroups.length > 0 ? (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3 pr-4">
                    {selectedGroups.map((group, index) => {
                      const lightingItem = lightingItems.find(
                        (item) => item.id === group.lightingId
                      );
                      return (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 border rounded-lg"
                        >
                          <div className="flex-1 space-y-2">
                            <Label className="text-sm font-medium">
                              {lightingItem
                                ? `${lightingItem.name || "Unnamed"} (${
                                    lightingItem.address
                                  })`
                                : `Group ${index + 1}`}
                            </Label>
                            <Combobox
                              options={lightingOptions}
                              value={group.lightingId?.toString() || ""}
                              onValueChange={(value) =>
                                handleGroupChange(
                                  index,
                                  value ? parseInt(value) : null
                                )
                              }
                              placeholder="Select lighting group..."
                              emptyText="No lighting found"
                            />
                          </div>
                          <div className="w-24 space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              Value
                            </Label>
                            <input
                              type="text"
                              value={group.value || ""}
                              onChange={(e) =>
                                handleValueChange(index, e.target.value)
                              }
                              className="w-full px-2 py-1 text-sm border rounded"
                              placeholder="0"
                            />
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveGroup(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Groups Selected</p>
                  <p className="text-sm mb-4">
                    Select lighting groups from the available list on the right.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Available Groups - Right Side */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Available Groups</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-2 pr-4">
                  {availableLightingGroups.length > 0 ? (
                    availableLightingGroups.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div>
                          <div className="font-medium text-sm">
                            {item.name || `Group ${item.address}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Address: {item.address}
                          </div>
                          {item.description && (
                            <div className="text-xs text-muted-foreground">
                              {item.description}
                            </div>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => handleAddFromAvailable(item)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <p className="text-sm">No available groups</p>
                      <p className="text-xs">
                        All lighting groups have been selected
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
